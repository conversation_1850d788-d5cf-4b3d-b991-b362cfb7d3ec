<?php

namespace Espo\Custom;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;

class Setup
{
    private $config;
    private $configWriter;

    public function __construct(Config $config, ConfigWriter $configWriter)
    {
        $this->config = $config;
        $this->configWriter = $configWriter;
    }

    public function afterInstall()
    {
        $this->addApertiaToNavigation();
    }

    public function afterUpgrade()
    {
        $this->addApertiaToNavigation();
    }

    private function addApertiaToNavigation()
    {
        try {
            $tabList = $this->config->get('tabList', []);

            if (!in_array('Apertia', $tabList)) {
                $tabList[] = 'Apertia';
                $this->configWriter->set('tabList', $tabList);
                $this->configWriter->save();
            }
        } catch (\Exception $e) {
            // Silent fail - don't break installation
        }
    }
}
