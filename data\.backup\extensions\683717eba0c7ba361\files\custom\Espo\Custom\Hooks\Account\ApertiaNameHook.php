<?php

namespace Espo\Custom\Hooks\Account;

use <PERSON>spo\Core\Hook\Hook\AfterSave;
use Espo\Core\ORM\Repository\Option\SaveOption;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\ORM\Repository\Option\SaveOptions;

/**
 * Adds " - Apertia" suffix to new Account names
 */
class ApertiaNameHook implements AfterSave
{
    public static int $order = 10;

    public function __construct(private EntityManager $entityManager) {}

    public function afterSave(Entity $entity, SaveOptions $options): void
    {
        if (!$entity->isNew() || $options->get('skipHooks')) {
            return;
        }

        $name = $entity->get('name');
        if ($name && !str_ends_with($name, ' - Apertia')) {
            $entity->set('name', $name . ' - Apertia');
            $this->entityManager->saveEntity($entity, [
                'skipHooks' => true,
                SaveOption::KEEP_NEW => false,
                SaveOption::KEEP_DIRTY => false
            ]);
        }
    }
}
