# AutoCRM 

## 🚀 Features

### ✅ **Account Hook**
- Automatically adds " - Apertia" suffix to new Account names
- Clean, efficient hook implementation

### ✅ **Apertia Custom Entity**
- Custom "Apertia" entity
- Automatic " - Apertia" suffix for new records
- Fully integrated with EspoCRM Entity Manager

### ✅ **Advanced Lead Find Contacts**
- Smart "Find Contacts" button on Lead detail view
- **Auto-search functionality** - automatically fills search box and triggers search
- Professional UI notifications (success/warning/error)
- Robust error handling and fallback mechanisms

## 📦 Installation

1. **Upload**: Administration > Extensions > Upload module zip
2. **Install**: Click Install button
3. **Clear Cache**: Administration > Clear Cache
4. **Rebuild**: Administration > Rebuild


### **Code:**
- ✅ **ES6+ JavaScript** with proper error handling
- ✅ **PHP 8.1+ features** with strict typing
- ✅ **DRY principle** - shared base class for hooks
- ✅ **Comprehensive JSDoc** documentation
- ✅ **Modular design** with single responsibility
- ✅ **Production-ready** error handling

### **Optimized Structure:**
```
Files/
├── client/custom/src/views/lead/record/detail.js    # Optimized Lead functionality
└── custom/Espo/Custom/
    ├── Hooks/
    │   ├── BaseApertiaNameHook.php                  # 🆕 Shared base class
    │   ├── Account/ApertiaNameHook.php              # Extends base (DRY)
    │   └── Apertia/ApertiaNameHook.php              # Extends base (DRY)
    └── Resources/metadata/
        ├── clientDefs/Apertia.json                  # 🆕 With building icon
        └── [other metadata files...]
```

## 🎯 Technical Specifications

- **EspoCRM**: 8.0.0+ compatible
- **PHP**: 8.1+ required
- **Architecture**: Modern, maintainable
- **Code Quality**: Developer standards

## 🔧 Advanced Features

### **Smart Search Implementation:**
- Multiple DOM selector fallbacks
- Automatic event triggering for EspoCRM compatibility
- Optimized timing for page load detection
- Professional UI feedback system

### **Robust Hook System:**
- Shared base class eliminates code duplication
- Proper dependency injection
- Skip hooks prevention for infinite loops
- Clean entity state management

