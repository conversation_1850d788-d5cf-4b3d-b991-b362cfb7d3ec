<?php
/************************************************************************
 * This file is part of EspoCRM.
 *
 * EspoCRM – Open Source CRM application.
 * Copyright (C) 2014-2025 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
 * Website: https://www.espocrm.com
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 * The interactive user interfaces in modified source and object code versions
 * of this program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU Affero General Public License version 3.
 *
 * In accordance with Section 7(b) of the GNU Affero General Public License version 3,
 * these Appropriate Legal Notices must retain the display of the "EspoCRM" word.
 ************************************************************************/

namespace Espo\Core\Formula\Functions\DatetimeGroup;

use Espo\Core\Di;
use Espo\Core\Utils\DateTime as DateTimeUtil;

use Espo\Core\Formula\ArgumentList;
use Espo\Core\Formula\Functions\BaseFunction;

use DateTime;
use Exception;

abstract class AddIntervalType extends BaseFunction implements Di\DateTimeAware
{
    use Di\DateTimeSetter;

    /**
     * @var bool
     */
    protected $timeOnly = false;

    /**
     * @var string
     */
    protected $intervalTypeString;

    public function process(ArgumentList $args)
    {
        $args = $this->evaluate($args);

        if (count($args) < 2) {
            $this->throwTooFewArguments();
        }

        $dateTimeString = $args[0];

        if (!$dateTimeString) {
            return null;
        }

        if (!is_string($dateTimeString)) {
            $this->throwBadArgumentType(1, 'string');
        }

        $interval = $args[1];

        if (!is_numeric($interval)) {
            $this->throwBadArgumentType(2, 'numeric');
        }

        $isTime = false;
        if (strlen($dateTimeString) > 10) {
            $isTime = true;
        }

        if ($this->timeOnly && !$isTime) {
            $dateTimeString .= ' 00:00:00';
            $isTime = true;
        }

        try {
            $dateTime = new DateTime($dateTimeString);
        } catch (Exception) {
            $this->log('bad date-time value passed', 'warning');

            return null;
        }

        $dateTime->modify(
            ($interval > 0 ? '+' : '') . strval($interval) . ' ' . $this->intervalTypeString
        );

        if ($isTime) {
            return $dateTime->format(DateTimeUtil::SYSTEM_DATE_TIME_FORMAT);
        } else {
            return $dateTime->format(DateTimeUtil::SYSTEM_DATE_FORMAT);
        }
    }
}
