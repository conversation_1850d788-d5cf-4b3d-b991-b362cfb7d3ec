<?php

namespace Espo\Custom;

use Espo\Core\Utils\Config;
use Espo\Core\Utils\Config\ConfigWriter;
use Espo\Core\InjectableFactory;

class Setup
{
    private $config;
    private $injectableFactory;

    public function __construct(Config $config, InjectableFactory $injectableFactory)
    {
        $this->config = $config;
        $this->injectableFactory = $injectableFactory;
    }

    public function afterInstall()
    {
        $this->addApertiaToNavigation();
    }

    public function afterUpgrade()
    {
        $this->addApertiaToNavigation();
    }

    private function addApertiaToNavigation()
    {
        try {
            $configWriter = $this->injectableFactory->create(ConfigWriter::class);
            $tabList = $this->config->get('tabList', []);

            // Remove Apertia if it already exists to avoid duplicates
            $tabList = array_filter($tabList, function($item) {
                return $item !== 'Apertia';
            });

            // Find the CRM divider and insert <PERSON>pertia right after it
            $newTabList = [];
            $apertiaAdded = false;

            foreach ($tabList as $item) {
                $newTabList[] = $item;

                // Check if this is the CRM divider
                if (is_object($item) && isset($item->text) && $item->text === '$CRM' && !$apertiaAdded) {
                    $newTabList[] = 'Apertia';
                    $apertiaAdded = true;
                }
            }

            // If CRM divider wasn't found, add Apertia at the beginning
            if (!$apertiaAdded) {
                array_unshift($newTabList, 'Apertia');
            }

            $configWriter->set('tabList', $newTabList);
            $configWriter->save();

        } catch (\Exception $e) {
            // Silent fail - don't break installation
        }
    }
}
