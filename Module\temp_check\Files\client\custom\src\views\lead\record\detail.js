/**
 * AutoCRM Lead Detail View Extension
 * Adds "Find Contacts" functionality to Lead detail view
 */
define('custom:views/lead/record/detail', ['crm:views/lead/record/detail'], function (Dep) {
    'use strict';

    return Dep.extend({

        /**
         * Find contacts with matching email address
         */
        actionFindContacts: function () {
            const leadEmail = this.model.get('emailAddress');

            if (!leadEmail) {
                const errorMsg = this.translate('No email address found for this lead', 'messages', 'Lead') ||
                                'Pro tento lead nebyla nalezena e-mailová adresa';
                Espo.Ui.error(errorMsg);
                return;
            }

            const searchingMsg = this.translate('Searching for contacts...', 'messages', 'Lead') ||
                               'Vyhledávání kontaktů...';
            Espo.Ui.notify(searchingMsg, 'info');

            this._searchContacts(leadEmail)
                .then(contacts => this._handleSearchResults(contacts, leadEmail))
                .catch(() => {
                    const errorMsg = this.translate('Error searching for contacts', 'messages', 'Lead') ||
                                   'Chyba při vyhled<PERSON> kontakt<PERSON>';
                    Espo.Ui.error(errorMsg);
                    Espo.Ui.notify(false);
                });
        },

        /**
         * Search for contacts by email
         * @param {string} email - Email address to search for
         * @returns {Promise<Array>} Array of contact names
         */
        _searchContacts: function (email) {
            const url = `Contact?where[0][type]=equals&where[0][attribute]=emailAddress&where[0][value]=${encodeURIComponent(email)}`;

            return Espo.Ajax.getRequest(url).then(response => {
                if (!response.list || response.list.length === 0) {
                    return [];
                }

                return response.list
                    .map(contact => {
                        const firstName = contact.firstName || '';
                        const lastName = contact.lastName || '';
                        return (firstName + ' ' + lastName).trim();
                    })
                    .filter(name => name);
            });
        },

        /**
         * Handle search results and navigate to contacts
         * @param {Array} contacts - Array of contact names
         * @param {string} email - Email address searched for
         */
        _handleSearchResults: function (contacts, email) {
            Espo.Ui.notify(false);

            if (contacts.length === 0) {
                const noContactsMsg = this.translate('No contacts found with email: {email}', 'messages', 'Lead').replace('{email}', email) ||
                                     `Nebyly nalezeny žádné kontakty s e-mailem: ${email}`;
                alert(noContactsMsg);
                return;
            }

            const foundMsg = this.translate('Found {count} contact(s) with email {email}', 'messages', 'Lead')
                .replace('{count}', contacts.length)
                .replace('{email}', email) ||
                `Nalezeno ${contacts.length} kontakt(ů) s e-mailem ${email}`;
            const message = `${foundMsg}:\n\n${contacts.join('\n')}`;
            alert(message);

            // Navigate to contacts and auto-search
            this.getRouter().navigate('#Contact/list', {trigger: true});
            this._autoFillSearch(email);
        },

        /**
         * Auto-fill search input and trigger search
         * @param {string} email - Email to search for
         */
        _autoFillSearch: function (email) {
            setTimeout(() => {
                const searchInput = this._findSearchInput();
                if (!searchInput) return;

                searchInput.value = email;
                this._triggerSearchEvents(searchInput);
                this._clickSearchButton();
            }, 1200);
        },

        /**
         * Find search input element using multiple selectors
         * @returns {HTMLElement|null} Search input element
         */
        _findSearchInput: function () {
            const selectors = [
                'input[data-name="textFilter"]',
                'input.form-control.text-filter',
                'input[type="search"]',
                '.search-container input'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) return element;
            }
            return null;
        },

        /**
         * Trigger input events to make EspoCRM recognize the change
         * @param {HTMLElement} input - Input element
         */
        _triggerSearchEvents: function (input) {
            ['input', 'keyup'].forEach(eventType => {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
        },

        /**
         * Click search button to trigger search
         */
        _clickSearchButton: function () {
            const searchButton = document.querySelector('button[data-action="search"]') ||
                                document.querySelector('.search-container button');
            if (searchButton) {
                searchButton.click();
            }
        }

    });
});
